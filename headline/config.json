{"task_config": {"task_id": "headline", "task_prompt": "prompt_ctr_v1.md"}, "train_config": {"epoch": 1, "batch_size": 10, "history_size": 20, "save_prompt_interval": 10, "train_sample_file": "dataset/creative_headline_LlmAsJudge_res_0516_train.csv", "eval_sample_file": "dataset/creative_headline_LlmAsJudge_res_0516_eval.csv", "eval_prompt": "prompt_ctr_v2.md"}, "model_config": {"model_selected": "model_1", "model_1": {"model_name": "gpt-4o-2024-11-20", "api_base": "https://search-va.byteintl.net/gpt/openapi/online/multimodal/crawl?ak=nd3GfsxmPWBkxlCIAZ2Hz3y6g0lHhEBB", "max_tokens": 16384, "timeout": 100}, "model_2": {"model_name": "gemini-2.5-pro-preview-05-06", "api_base": "https://gpt-i18n.byteintl.net/gpt/openapi/online/v2/crawl?ak=AHeiwKbjb9hzXbrkArJsH5nJM6xfkyB1", "max_tokens": 16384, "timeout": 100}}}