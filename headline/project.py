import re
import os
import random
import base64
import pandas as pd
from PIL import Image
from pathlib import Path
from dataclasses import dataclass
from autoprompt import AutoPromptEngine

class APTrain(AutoPromptEngine):
    def __init__(self, config, train_or_eval):
        super().__init__(config, train_or_eval)

    def set_teacher_prompt_template(self, index,
                                        prompt,
                                        StudentPredictResult,
                                        TeacherTrueResult,
                                        summary,
                                        history) -> str:
        return f"""
# 角色
* 你是一个图像和文本多模态的分析和理解专家，也是一位经验非常丰富的广告创意专业人士，非常擅长判断广告标题的质量。
* 同时你也是一名资深教师, 你带了一名学生, 你要教会他自主完成广告标题多维度质量打分的任务。

# 现状
## 这是教师第{index}次教学
## 学生端的prompt是:
{prompt}
## 学生识别的结果是:
{StudentPredictResult}
## 参考结果是:
{TeacherTrueResult}
## 教师上次的思考总结是：
{summary}
## 教师的历史教学过程是：
{history}

# 教学任务和流程 
## 教师任务
### 任务定义        
* 你需要根据学生的输出结果和参考结果，分析学生的输出结果和参考结果之间的正相关性程度，形成学生表现评估；
* 结合你的历史教学记录，根据学生的表现评估结果，结合学生的任务定义、输出格式要求，来调整和生成学生的prompt, 并把思考结果( summary ) 总结下来；
* 预期目标：让学生使用新的prompt后，学生的输出结果和参考结果之间的正相关性程度更高，学生的输出结果和参考结果之间的正相关性越高，学生的表现越好。
### 补充说明
* 参考结果是经过标注人员筛选确认合理有效的标准参考信息，是标准答案
* 不能将参考结果直接告诉学生, 这是作弊行为 
### 教师输出格式
* 直接输出'plan'的原始json格式，不加\"'''json\"
* 输出结果为summary和学生prompt
* 输出结果示例: 
{{
    "summary": "新的思考总结",
    "prompt": "调整后的prompt"
}}
## 学生任务
### 任务定义
* 学生需要根据教师提供的学生prompt, 完成广告标题多维度质量打分的任务。
* 广告标题多维度质量打分的维度包括：
    ** Key Info Prioritization & Upfront
        *** a score from 1 to 3，1 being the lowest and 3 being the highest
    ** Info Density & Richness
        *** a score from 1 to 3，1 being the lowest and 3 being the highest
    ** Urgency & CTA Power
        *** a score from 1 to 3，1 being the lowest and 3 being the highest
    ** Industry-Specific Resonance
        *** a score from 1 to 3，1 being the lowest and 3 being the highest
    ** Personalization
        *** a score from 1 to 3，1 being the lowest and 3 being the highest
    ** Overall Score
        *** a score from 1 to 5，1 being the lowest and 5 being the highest
### 学生输出格式
* 直接输出'plan'的原始json格式，不加\"'''json\"
* 输出结果为各个维度的打分结果
* 根据输入样本数量，输出结果为一个dict, dict key为sample的index, value为各个维度的打分结果
* 输出结果示例:
{{
  'sample0': {
        'Key Info Prioritization & Upfront': value,
        'Info Density & Richness': value,
        'Urgency & CTA Power': value,
        'Industry-Specific Resonance': value,
        'Personalization': value,
        'Overall Score': value
    }
}}
        """

    def get_student_output_format(self):
        return """
# Output Format:
* Output MUST BE json format in English or Chinese
* Each dict key is the metric name, value is the metric value
* In the JSON result, only the key and its corresponding value are needed, without any additional information.
* The value of each key MUST BE between 0.0 and 1.0
* The Json result template is as following
{
    'sample0': {
        'overall_score': value, 
    },
    'sample1': {
        'overall_score': value, 
    },
    'sample2': {
        'overall_score': value, 
    },
    'sample3': {
        'overall_score': value, 
    },
    'sample4': {
        'overall_score': value, 
    },
    'sample5': {
        'overall_score': value, 
    },
    'sample6': {
        'overall_score': value, 
    },
    'sample7': {
        'overall_score': value, 
    },
    'sample8': {
        'overall_score': value, 
    },
    'sample9': {
        'overall_score': value, 
    }
}
        """


    def define_modal_label(self):
        self.image_modal_key   = []
        self.text_modal_key    = ["headline"]
        # self.teacher_label_key = ["Key Info Prioritization & Upfront", "Info Density & Richness", "Urgency & CTA Power", "Industry-Specific Resonance", "Personalization", "Overall Score"]
        self.teacher_label_key = ["ctr"]

    def load_samples(self, train_sample_file) -> tuple[list[tuple[int, dict[str, str]]], dict[str, str]]:
        """
        读取学习样本文件，检查文件格式是否为 CSV, 以及是否包含所需的列。
        将处理后的数据存储在 self.learn_sample 中。
        """
        # 检查文件格式是否为 CSV
        if not train_sample_file or not train_sample_file.lower().endswith('.csv'):
            raise ValueError("请提供有效的CSV样本文件路径。")
            
        try:
            # 读取csv, 检查是否包含所需的列
            df = pd.read_csv(train_sample_file, delimiter=',')
            required_columns = self.image_modal_key + self.text_modal_key+ self.teacher_label_key
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"文件 {train_sample_file} 缺少必要的列 {col}。")
            
            # read sample as dict list
            samples = []
            for index, row in df.iterrows():
                image_path_list    = [row.get(key) for key in self.image_modal_key]
                text_modal_list    = [row.get(key) for key in self.text_modal_key]
                teacher_label_list = [row.get(key) for key in self.teacher_label_key]

                sample_dict = {
                    "image_modal_list": image_path_list,
                    "text_modal_list": text_modal_list,
                    "teacher_label_list": teacher_label_list
                }
                samples.append(sample_dict)

            return samples
        except Exception as e:
            raise ValueError(f"读取文件 {train_sample_file} 时出错: {str(e)}")


    