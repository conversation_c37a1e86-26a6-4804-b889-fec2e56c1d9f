import re
import os
import random
import base64
import pandas as pd
from PIL import Image
from pathlib import Path
from dataclasses import dataclass
from autoprompt import AutoPromptEngine

class APTrain(AutoPromptEngine):
    def __init__(self, config, train_or_eval):
        super().__init__(config, train_or_eval)

    def set_teacher_prompt_template(self, index,
                                        prompt,
                                        StudentPredictResult,
                                        TeacherTrueResult,
                                        summary,
                                        history) -> str:
        return f"""
# Role
* 你是一个图像分析和理解专家，
* 同时你也是一个Teacher, 你带了一个Student, 你要协助他完成图像分析和理解任务。

# Background
## Student
* student是一个多模态的图像分析和理解模型, 能够根据prompt和输入的两张image, 分析第二张图在下列维度是否为badcase。
* 维度包括: 尺寸过大、显著性、位置、完整性、模特的strange layout、字体样式、字体颜色、布局、和商品的匹配性、真实性
* 分析的维度是固定的, 你只需要输出对应的value值
* the value of each key MUST BE one of the following: "是badcase"、"不是badcase"、"无法判断"

## Notice 
* 你只能通过修改prompt的方式来协助student来完成工作
* 不能将图片中的文字信息告诉student, 这是作弊行为 
## 期望的结果
* 你的教学行为会讲过多轮迭代, 最终你的student在你的prompt的指导下, 出色完成提取工作。

# WorkFlow 
1. 你的Student会分析对比输入的两张图片, 并对第二张图的下列维度进行分析, 并返回各个维度的是否为badcase的取值
* 维度包括: 尺寸过大、显著性、位置、完整性、模特的strange layout、字体样式、字体颜色、布局、和商品的匹配性、真实性
* 分析的维度是固定的, 你只需要输出对应的value值, json结果中只需要有key和对应的value, 不需要有任何其他额外信息
* the value of each key MUST BE one of the following: "是badcase"、"不是badcase"、"无法判断"
2. 我会提供TeacherTrueResult供你参考
* 参考的TeacherTrueResult是经过标注人员筛选确认合理有效的
* 注意student返回的StudentPredictResult可能存在一些大小写字符的差异, 这是符合预期的，只要全小写一致即可

3. 我会把前几次的教学过程反馈给你。
4. 你要总结和反思, 并把思考结果( summary ) 总结下来，协助你自己在下一轮教学中更有效地完成工作
5. 调整prompt, 让你的student能更好的完成工作
* 注意要多从图像结构上分析问题，不要纠结太微小的字符差异

# Current Status 
## 这是你第{index}次教学
## 当前的prompt是:
{prompt}
## Student识别的结果是:
{StudentPredictResult}
## 参考结果是：
{TeacherTrueResult}
## 你上次的思考总结是：
{summary}
## 你的历史教学过程是：
{history}

# 任务
## Step1. 分析&思考  
* 评估你的student的表现
* 一定要结合历史记录
* 反思自己的教学方法，并总结你的思考结果
## Step2. 调整prompt 
* 根据你的思考结果调整student的prompt 
## Step3. 输出结果
* 输出json格式: 
{{
    "summary": "新的思考总结",
    "prompt": "调整后的prompt"
}}
                """

    def define_modal_label(self):
        self.image_modal_key   = ["input_image", "out_image"]
        self.text_modal_key    = ["prompt"]
        self.teacher_label_key = ["尺寸过大", "显著性", "位置", "完整性", "模特的strange layout", "字体样式", "字体颜色", "布局", "和商品的匹配性", "真实性"]

    def load_samples(self, train_sample_file) -> tuple[list[tuple[int, dict[str, str]]], dict[str, str]]:
        """
        读取学习样本文件，检查文件格式是否为 CSV, 以及是否包含所需的列。
        将处理后的数据存储在 self.learn_sample 中。
        """
        # 检查文件格式是否为 CSV
        if not train_sample_file or not train_sample_file.lower().endswith('.csv'):
            raise ValueError("请提供有效的CSV样本文件路径。")
            
        try:
            # 读取csv, 检查是否包含所需的列
            df = pd.read_csv(train_sample_file, delimiter=',')
            required_columns = self.image_modal_key + self.text_modal_key+ self.teacher_label_key
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"文件 {train_sample_file} 缺少必要的列 {col}。")
            
            # read sample as dict list
            samples = []
            for index, row in df.iterrows():
                image_path_list    = [row.get(key) for key in self.image_modal_key]
                text_modal_list    = [row.get(key) for key in self.text_modal_key]
                teacher_label_list = [row.get(key) for key in self.teacher_label_key]

                sample_dict = {
                    "image_modal_list": image_path_list,
                    "text_modal_list": text_modal_list,
                    "teacher_label_list": teacher_label_list
                }
                samples.append(sample_dict)

            return samples
        except Exception as e:
            raise ValueError(f"读取文件 {train_sample_file} 时出错: {str(e)}")


    