import re
import os
import random
import base64
import pandas as pd
from PIL import Image
from pathlib import Path
from dataclasses import dataclass
from autoprompt import AutoPromptEngine

class APTrain(AutoPromptEngine):
    def __init__(self, config, train_or_eval):
        super().__init__(config, train_or_eval)

    def set_teacher_prompt_template(self, index,
                                        prompt,
                                        StudentPredictResult,
                                        TeacherTrueResult,
                                        summary,
                                        history) -> str:
        return f"""
# Role
* 你是一个图像识别专家，善于处理图像中的文字信息。
* 同时你也是一个Teacher, 你带了一个Student, 你要协助他完成图像识别任务。

# Background
## Student
* student是一个多模态的图像识别模型, 能够根据prompt和输入的image，提取图片中的 KeyText。

## Notice 
* 你只能通过修改prompt的方式来协助student来完成工作
* 不能将图片中的文字信息告诉student，这是作弊行为 
## 期望的结果
* 你的教学行为会讲过多轮迭代，最终你的student在你的prompt的指导下，出色完成提取工作。

# WorkFlow 
1. 你的Student会识别一张图像，并返回StudentPredictResult
2. 我会提供TeacherTrueResult供你参考
* 参考的TeacherTrueResult是OCR工具逐行提取，并经过标注人员筛选。
* 注意student返回的StudentPredictResult可能是一个跨行段落，与参考的TeacherTrueResult格式可能不一致，这是符合预期的，不要求逐行分解文本
* 注意student返回的StudentPredictResult可能存在一些错误的字符，这是符合预期的，只要大概相似即可

3. 我会把前几次的教学过程反馈给你。
4. 你要总结和反思, 并把思考结果( summary ) 总结下来，协助你自己在下一轮教学中更有效地完成工作
5. 调整prompt，让你的student能更好的完成工作
* 注意要多从图像结构上分析问题，不要纠结太微小的字符差异

# Current Status 
## 这是你第{index}次教学
## 当前的prompt是：
{prompt}
## Student识别的结果是：
{StudentPredictResult}
## 参考结果是：
{TeacherTrueResult}
## 你上次的思考总结是：
{summary}
## 你的历史教学过程是：
{history}

# 任务
## Step1. 分析&思考  
* 评估你的student的表现
* 一定要结合历史记录
* 反思自己的教学方法，并总结你的思考结果
## Step2. 调整prompt 
* 根据你的思考结果调整student的prompt 
## Step3. 输出结果
* 输出json格式: 
{{
    "summary": "新的思考总结",
    "prompt": "调整后的prompt"
}}
                """

    def define_modal_label(self):
        self.image_modal_key = ["image_path"]
        self.text_modal_key  = []
        self.teacher_label_key = ["label"]

    def load_samples(self, train_sample_file) -> tuple[list[tuple[int, dict[str, str]]], dict[str, str]]:
        """
        读取学习样本文件，检查文件格式是否为 CSV，以及是否包含所需的列。
        将处理后的数据存储在 self.learn_sample 中。
        """
        # 检查文件格式是否为 CSV
        if not train_sample_file or not train_sample_file.lower().endswith('.csv'):
            raise ValueError("请提供有效的CSV样本文件路径。")
            
        try:
            df = pd.read_csv(train_sample_file, delimiter='\t')
            # 检查是否包含所需的列
            required_columns = self.image_modal_key + self.text_modal_key + self.teacher_label_key
            for col in required_columns:
                if col not in df.columns:
                    raise ValueError(f"文件 {train_sample_file} 缺少必要的列 {col}。")
            
            # read sample as dict list
            samples = []
            for index, row in df.iterrows():
                image_path_list    = [row.get(key) for key in self.image_modal_key]
                text_modal_list    = [row.get(key) for key in self.text_modal_key]
                teacher_label_list = [row.get(key)[1:-1].strip().split(',') for key in self.teacher_label_key]

                sample_dict = {
                    "image_modal_list": image_path_list,
                    "text_modal_list": text_modal_list,
                    "teacher_label_list": teacher_label_list
                }
                samples.append(sample_dict)
                
            return samples
        except Exception as e:
            raise ValueError(f"读取文件 {train_sample_file} 时出错: {str(e)}")


    