from PIL import Image
import io
import base64

def gen_image_message(image_byte: bytes):
    """
    将图片字节流编码为base64，并生成用于GPT接口的image_url结构
    """
    image = Image.open(io.BytesIO(image_byte)).convert("RGB")
    buffer = io.BytesIO()
    image.save(buffer, format='JPEG')
    b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    img_struct = dict(url=f'data:image/jpeg;base64,{b64}')
    return dict(type='image_url', image_url=img_struct) 