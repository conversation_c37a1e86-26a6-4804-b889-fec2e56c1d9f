import os
import json
import random
import logging
from tqdm import tqdm
import requests
import time
from datetime import date
from pathlib import Path
from PIL import Image
import io
import pandas as pd
from typing import List
from .image_utils import gen_image_message
from abc import abstractmethod




class AutoPromptEngine:
    def __init__(self, config: dict, train_or_eval: str):
        # 初始化日志
        self.train_or_eval = train_or_eval
        self.setup_logging(config)

        # 初始化任务配置
        self.task_config  = config["task_config"]
        self.model_config = config["model_config"]
        self.train_config = config["train_config"]
        self.task_config["task"]  = self.set_task_prompt()
        
        # 设置模态标签信息
        self.define_modal_label()

        # 相对路径扩展为绝对路径,加载样本
        if self.train_or_eval == "train":
            train_sample_file = os.path.join(os.getcwd(), self.train_config["train_sample_file"])
            self.sample_loader = self.load_samples(train_sample_file)
            logging.info(f"load {len(self.sample_loader)} samples from {train_sample_file}")
            # random shuffle samples
            random.shuffle(self.sample_loader)
        else:
            eval_sample_file = os.path.join(os.getcwd(), self.train_config["eval_sample_file"])
            self.sample_loader = self.load_samples(eval_sample_file)
            logging.info(f"load {len(self.sample_loader)} samples from {eval_sample_file}")

    def set_task_prompt(self) -> str:
        with open(os.path.join(os.getcwd(), self.task_config["task_id"], self.task_config["task_prompt"]), "r") as f:
            self.task_config["task_prompt"] = f.read()
        return self.task_config["task_prompt"]
    
    def get_evaluate_prompt(self) -> str:
        with open(os.path.join(os.getcwd(), self.task_config["task_id"], self.train_config["eval_prompt"]), "r") as f:
            self.train_config["eval_prompt"] = f.read()
        return self.train_config["eval_prompt"]

    @abstractmethod
    def define_modal_label(self):
        pass

    @abstractmethod
    def load_samples(self, train_sample_file) -> tuple[list[tuple[int, dict[str, str]]], dict[str, str]]:
        pass

    @abstractmethod
    def set_teacher_prompt_template(self, index,
                                            prompt,
                                            StudentPredictResult,
                                            TeacherTrueResult,
                                            summary) -> str:
        pass
        
    def setup_logging(self, config):
        if self.train_or_eval == "train":
            filename=f'{os.path.join(os.getcwd(), "logs")}/{config["task_config"]["task_id"]}_autopromptengine.log'
        else:
            filename=f'{os.path.join(os.getcwd(), "logs")}/{config["task_config"]["task_id"]}_eval.log'
        logging.basicConfig(
            filename=filename,
            level=logging.INFO,
            format='\n%(asctime)s - %(levelname)s - %(message)s'
        )

    def update_summary_prompt(self, teacher_result):
        # check the key of teacher_result
        # using previous summary or prompt if not exist
        if "summary" in teacher_result:
            self.summary = teacher_result['summary']
        if "prompt" in teacher_result:
            self.prompt = teacher_result['prompt']

    def update_history(self, meta):
        his = meta.copy()
        his.pop("prompt") 
        self.history.append(his)
        self.history = self.history[-self.train_config["history_size"]:]
        
        # log history
        log_message = (
            f"Key Results of Teaching {meta['index']} time: \n"
            f"StudentPredictResult: {json.dumps(meta['StudentPredictResult'], indent=4, ensure_ascii=False)}\n"
            f"TeacherTrueResult: {json.dumps(meta['TeacherTrueResult'], indent=4, ensure_ascii=False)}\n"
            f"prompt:↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓\n"
            f"{meta['prompt']}\n"
            "summary:↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓\n"
            f"{meta['summary']}\n"
            "End↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑End\n\n\n"
        )
        logging.info(log_message)

    # define student result dict template
    def get_student_output_format(self):
        sample_dict = {key: "value" for key in self.teacher_label_key}
        result_list = [sample_dict] * self.train_config["batch_size"]
        sample_tag  = [f"sample{i}" for i in range(self.train_config["batch_size"])]
        result_dict = {tag: sample_dict for tag, sample_dict in zip(sample_tag, result_list)}

        return f"""

# Output Format:
* Output MUST BE json format in English or Chinese
* Each dict key is the metric name, value is the metric value
* In the JSON result, only the key and its corresponding value are needed, without any additional information.
* The Json result template is as following
{{
    {result_dict}
}}
        """

    def run(self):
        """
        执行学习代理的主要流程，包括学生推理、教师推理和日志记录。
        """
        if self.train_or_eval == "train":
            logging.info(f"BEGIN TRAINING TASK : \n{self.task_config['task']}")
            self.train()
        else:
            logging.info(f"BEGIN EVALUATION TASK : \n{self.task_config['task']}")
            self.evaluate()

    def train(self):
        """
        执行学习代理的主要流程，包括学生推理、教师推理和日志记录。
        """
        iter_index = 0

        # 初始化meta
        self.history = []
        self.summary = "first teaching, summary is empty"
        self.prompt = self.task_config["task"]

        final_prompt_file = os.path.join(os.getcwd(), "finalprompts", f"{self.task_config['task_id']}.md")

        # loop for train
        for epoch in range(self.train_config["epoch"]):
            for i in tqdm(range(0, len(self.sample_loader), self.train_config["batch_size"]), 
                        desc=f"Epoch {epoch+1}/{self.train_config['epoch']}", 
                        total=len(self.sample_loader)//self.train_config["batch_size"]):
                sample_list = self.sample_loader[i:i+self.train_config["batch_size"]]
            
                # log
                iter_index += 1
                logging.info(f"begin train iter_index: {iter_index}")
                logging.info(f"sample_list: {sample_list}")

                # student inference
                # student_prompt = self.prompt + self.get_student_output_format()
                student_prompt = self.prompt
                logging.info(f"student prompt: \n{student_prompt}")
                student_result_dict = self.inference(sample_list, student_prompt, type_="student")
                logging.info(f"student output: \n{student_result_dict}")

                # teacher_label: list of dict 
                # combin teacher_label_list with self.teacher_label_key to dict
                sample_teacher_label_list = [dict(zip(self.teacher_label_key, sample.get("teacher_label_list"))) for sample in sample_list]
                sample_tag  = [f"sample{i}" for i in range(self.train_config["batch_size"])]
                sample_teacher_label_dict = {tag: sample_dict for tag, sample_dict in zip(sample_tag, sample_teacher_label_list)}

                # set teaching information
                teaching_info = {}
                teaching_info["index"] = iter_index
                teaching_info["prompt"] = self.prompt
                teaching_info["StudentPredictResult"] = student_result_dict
                teaching_info["TeacherTrueResult"] = sample_teacher_label_dict
                teaching_info["summary"] = self.summary
                teaching_info["history"] = self.history

                # teacher inference
                tec_res = self.inference(sample_list, teaching_info, type_="teacher")
                logging.info(f"teacher output: \n{tec_res}")
                self.update_summary_prompt(tec_res)

                # update history
                meta_info = {}
                meta_info["index"] = iter_index
                meta_info["prompt"] = self.prompt
                meta_info["StudentPredictResult"] = student_result_dict
                meta_info["TeacherTrueResult"] = sample_teacher_label_dict
                meta_info["summary"] = self.summary
                self.update_history(meta_info)

                # save prompt
                if iter_index % self.train_config["save_prompt_interval"] == 0:
                    # save_prompt = self.prompt + self.get_student_output_format()
                    save_prompt = self.prompt
                    with open(final_prompt_file, "a", encoding="utf-8") as f:
                        f.write(f"\n# {time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))}\n")
                        f.write(f"# prompt after {iter_index} iterations: \n")
                        f.write(f"{save_prompt}")
                    logging.info(f"save final prompt to {final_prompt_file}")   
                    logging.info(f"FINAL PROMPT: \n{save_prompt}")


    def inference(self, sample_list, prompt, type_: str = "student"):
        """
        调用 GPT 接口进行推理，并尝试解析返回的 JSON 数据。

        :param sample_list: 图像模态的数据, batch list of sample dict
        :param prompt: 用于 GPT 模型的提示文本。
        :param type_: 推理类型，可选值为 "student" 或 "teacher"，默认为 "student"。
        :return: 解析后的 JSON 数据和错误代码 (0 表示成功, 1 表示重试后仍失败, 2 表示类型无效)。
        """
        if type_ not in ["student", "teacher"]:
            logging.error(f"无效的推理类型: {type_}")
            return None

        # check sample_list should be list
        if not isinstance(sample_list, list):
            logging.error(f"sample_list 应该是一个列表, 但实际类型是: {type(sample_list)}")
            return None

        try:
            if type_ == "student":
                res = self.student(sample_list, prompt)
            else:
                res = self.teacher(sample_list, prompt)

            # 尝试解析 JSON, 找到第一个"{"和最后一个"}"的位置
            logging.info(f"result: \n{res}")
            # res = res[res.find('{'):res.rfind('}')+1]
            res = res.strip("'''").strip("'''json")
            result = json.loads(res)

            # 如果解析成功，退出循环
            return result

        except json.JSONDecodeError as e:
            logging.error(f"{type_} inference, JSON 解析失败，错误详情：{str(e)}")
            logging.error(f"{type_} result: \n{res}")
        except Exception as e:
            logging.error(f"{type_} sample_list: \n{sample_list}")
            logging.error(f"未知错误: {str(e)}")

    def student(self, sample_list:list, prompt:str):
        message_list = self.set_message(sample_list, prompt)
        result_list, token_length_error_tag = self.request_gpt(message_list, temperature=0.0)
        return result_list

    def teacher(self, sample_list:list, teaching_info:dict): 
        # reduce history to try again when token_length_error_tag is True
        try_index = 0
        while try_index < 2:
            teacher_prompt = self.set_teacher_prompt_template(
                    index=teaching_info["index"],
                    prompt=teaching_info["prompt"],
                    StudentPredictResult=teaching_info["StudentPredictResult"],
                    TeacherTrueResult=teaching_info["TeacherTrueResult"],
                    summary=teaching_info["summary"],
                    history=teaching_info["history"]
                )
            logging.info(f"teacher prompt: \n{teacher_prompt}")
            message_list = self.set_message(sample_list, teacher_prompt)
            result, token_length_error_tag = self.request_gpt(message_list, temperature=0.8)
            if token_length_error_tag:
                # reduce length of history to half
                self.train_config["history_size"] = self.train_config["history_size"] // 2
                teaching_info["history"] = teaching_info["history"][len(teaching_info["history"]) // 2 : ]
                self.history = teaching_info["history"]
                logging.info(f"reduce history to half, now history length is {len(teaching_info['history'])}")
                try_index += 1
                continue
            else:
                break
        return result

    def set_message(self, sample_list:list, prompt:str):
        message_list = []
        for sample_index, sample in enumerate(sample_list):
            # image modal 
            image_message_list = []
            for image_path in sample["image_modal_list"]:
                # path begin with http or https
                if image_path.startswith( 'http' ):  
                    image_message = dict(type="image_url", image_url=dict(url=image_path))
                # path begin with local path
                else:
                    image_message = gen_image_message(Path(image_path).read_bytes())
                image_message_list.append(image_message)

            # text modal
            text_message_list = []
            for text_modal in sample["text_modal_list"]:
                text_modal = text_modal.replace("\n", "").replace("\"", "")
                text_message_list.append(dict(type="text", text=text_modal))

            # sample message
            sample_message = [dict(type="text", text=f"Upper is the image modal and text modal of sample {sample_index}")]
            message = dict(
                role="user",
                content = image_message_list + text_message_list + sample_message + [dict(type="text", text=prompt)]
            )
            message_list.append(message)
        return message_list

    def request_gpt(self, messages: list,temperature=0.0, max_try: int = 10) -> tuple:
        model_config   = self.model_config[self.model_config["model_selected"]]
        max_tokens     = model_config["max_tokens"]
        timeout        = model_config["timeout"]
        model_name     = model_config["model_name"]
        api_base       = model_config["api_base"]

        headers = {
            'Content-Type': 'application/json'
        }
        payload = dict(
            model=model_name,
            messages=messages,
            max_tokens=max_tokens,
            n=1,
            temperature=temperature,
            response_format={"type": "json_object"}
        )
        data_str = json.dumps(payload)
        err_cnt = 0
        response = None
        timeout_try = timeout

        answer = []
        token_length_error_tag = False

        while err_cnt < max_try:
            error_tag = False
            try:
                response = requests.post(api_base, headers=headers, data=data_str, timeout=timeout_try)
                if response is None:
                    logging.error('[request_gpt] error: No response')
                    error_tag = True
                else:
                    ret_code = response.status_code
                    ret_code = 0 if (200 <= int(ret_code) < 300) else ret_code
                    if ret_code!= 0:
                        logging.error(f'[request_gpt] error: {ret_code} {response.text}')
                        error_tag = True
                    if "This model's maximum context length" in response.text:
                        logging.error(f'[request_gpt] error：token length exceeded, history will be cleared')
                        token_length_error_tag = True

            except Exception as e:
                logging.error(f'[request_gpt] post error: {e}')
                error_tag = True

            if error_tag:
                err_cnt += 1
                timeout_try = timeout * (err_cnt + 1)
                time.sleep(5)
                continue
            else:
                resp_struct = response.json()
                answer = resp_struct['choices'][0]['message']['content'].strip()
                break

            if token_length_error_tag:
                break
        
        return answer, token_length_error_tag

    # 增加evaluate方法，输入样本，调用student方法，计算每个label key的结果
    def evaluate(self):
        # 初始化
        result_list = []
        teacher_label_list = []
        evaluate_prompt = self.get_evaluate_prompt()
        logging.info(f"evaluate_prompt: \n{evaluate_prompt}")

        # loop for train
        for i in tqdm(range(0, len(self.sample_loader)), 
                    total=len(self.sample_loader)):
            sample = [self.sample_loader[i]]
            # if i == 2:
            #     break
        
            # log
            logging.info(f"sample: {sample}")

            # student inference
            result_dict = self.inference(sample, evaluate_prompt, type_="student")
            # get the first dict key
            result_dict = list(result_dict.values())[0]

            # teacher_label: list of dict 
            teacher_label_dict = dict(zip(self.teacher_label_key, sample[0].get("teacher_label_list")))

            # update result
            result_list.append(result_dict)
            teacher_label_list.append(teacher_label_dict)

        # prediction文件夹下新建一个文件夹，用于存储预测结果
        # Create prediction directory if it doesn't exist
        prediction_dir = os.path.join(os.getcwd(), "prediction")
        os.makedirs(prediction_dir, exist_ok=True)

        # Create file path for xlsx, with timeinformation
        result_file = os.path.join(prediction_dir, f"{self.task_config['task_id']}_results_{date.today().strftime('%Y-%m-%d')}.xlsx")
        
        # Convert lists to DataFrames
        prediction_df = pd.DataFrame(result_list)
        groundtruth_df = pd.DataFrame(teacher_label_list)
        prompt_df = pd.DataFrame({'evaluate_prompt': [evaluate_prompt]})
        
        # Write to Excel with different sheets
        with pd.ExcelWriter(result_file) as writer:
            prediction_df.to_excel(writer, sheet_name='Predictions', index=False)
            groundtruth_df.to_excel(writer, sheet_name='Ground Truth', index=False)
            prompt_df.to_excel(writer, sheet_name='Prompt', index=False)

        # log
        logging.info(f"save results to {result_file}")
        print(f"save results to {result_file}")

        # metric
        self.metrics(result_list, teacher_label_list)

    def metrics(self, student_result_list, teacher_label_list):
        """
        计算每个metric的值, student输出和teacher label一致,则metric值为1, 否则为0
        求每个metric的平均值    
        """
        # list length check
        if len(student_result_list) != len(teacher_label_list):
            logging.error(f"student_result_list and teacher_label_list length not match, evaluation failed in this iteration")
            return

        # each sample
        recall = {key: 0 for key in self.teacher_label_key}
        for i in range(len(teacher_label_list)):
            student_result = student_result_list[i]
            teacher_label  = teacher_label_list[i]
            for metric in self.teacher_label_key:
                if metric in student_result and metric in teacher_label:
                    recall[metric] += 1 if student_result[metric] == teacher_label[metric] else 0
                else:
                    recall[metric] += 0
        
        # 求每个metric的平均值
        evaluate_result_avg = {}
        for metric in self.teacher_label_key:
            evaluate_result_avg[metric] = recall[metric] / len(teacher_label_list)

        return evaluate_result_avg