import asyncio
from autogen_agentchat.agents import Assistant<PERSON>gent
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient, AzureOpenAIChatCompletionClient
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image
from io import BytesIO
import requests
import <PERSON><PERSON>
from typing import Literal
from pydantic import BaseModel

# Define a model client. You can use other model client that implements
# the `ChatCompletionClient` interface.
model_client = AzureOpenAIChatCompletionClient(
    model="gpt-4o-2024-11-20",
    api_key="nd3GfsxmPWBkxlCIAZ2Hz3y6g0lHhEBB",
    azure_endpoint="https://search-va.byteintl.net/gpt/openapi/online/multimodal/crawl",
    api_version="2024-11-20",
)

# model_client = AzureOpenAIChatCompletionClient(
#     model="gemini-2.5-pro-preview-05-06",
#     api_key="AHeiwKbjb9hzXbrkArJsH5nJM6xfkyB1",
#     azure_endpoint="https://search-va.byteintl.net/gpt/openapi/online/multimodal/crawl",
#     api_version="2025-05-06",
# )
# print(model_client.dump_component().model_dump_json())

# model_client = OpenAIChatCompletionClient(
#     model="qwen/qwen2.5-vl-3b-instruct:free",
#     # base_url="https://openrouter.ai/api/v1",
#     endpoint="https://openrouter.ai/api/v1",
#     api_key="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",
#     model_info={
#         "family": "unknown",
#         "vision": False,
#         "function_calling": True,
#         "json_output": False,
#         "structured_output": True,
#     }
# )

# Define a simple function tool that the agent can use.
# For this example, we use a fake weather tool for demonstration purposes.
async def get_weather(city: str) -> str:
    """Get the weather for a given city."""
    return f"The weather in {city} is 73 degrees and Sunny."


# The response format for the agent as a Pydantic base model.
class AgentResponse(BaseModel):
    thoughts: str
    response: Literal["happy", "sad", "neutral"]

# Define an AssistantAgent with the model, tool, system message, and reflection enabled.
# The system message instructs the agent via natural language.
agent = AssistantAgent(
    name="assistant",
    model_client=model_client,
    # tools=[get_weather],
    system_message="You are a helpful assistant.",
    reflect_on_tool_use=True,
    model_client_stream=True,  # Enable streaming tokens from the model client.
    output_content_type=AgentResponse, # Define the output content type of the agent.
)





###############################################################
# Create a multi-modal message with random image and text.
pil_image = PIL.Image.open("/Users/<USER>/Downloads/promptimages/ai_img_zenith_1742907023677185445_rfDqRV.jpeg")
img = Image(pil_image)
multi_modal_message = MultiModalMessage(content=["Can you describe the content of this image?", img], source="user")
img
###############################################################

# Run the agent and stream the messages to the console.
async def main() -> None:
    await Console(agent.run_stream(task="What is the weather in New York?"))
    await Console(agent.run_stream(task=multi_modal_message), output_stats=True)
    # Close the connection to the model client.
    await model_client.close()


# NOTE: if running this inside a Python script you'll need to use asyncio.run(main()).
asyncio.run(main())