# AutoPromptProjects

## 简介

AutoPromptProjects 是一个基于大型语言模型（LLM）的自动提示工程框架，专注于图像理解和文本提取任务。该框架实现了一种教师-学生模式的自动提示优化方法，通过迭代优化提示（prompt）来提高模型在特定任务上的表现。

主要特点：
- 自动优化提示工程（Auto Prompt Engineering）
- 教师-学生学习模式（Teacher-Student Learning）
- 支持多模态任务，特别是图像中的文本提取和广告标题评估
- 可扩展的项目结构，便于添加新的任务类型

## 项目结构

```
autoprompt/
├── README.md                 # 项目说明文档
├── main.py                   # 主入口文件
├── autoprompt/               # 核心框架
│   ├── __init__.py           # 包初始化
│   ├── autopromptengine.py   # 自动提示引擎
│   └── image_utils.py        # 图像处理工具
├── keytext/                  # 示例项目：关键文本提取
│   ├── __init__.py           # 包初始化
│   ├── config.json           # 项目配置
│   └── project.py            # 项目实现
├── headline/                 # 示例项目：广告标题评估
│   ├── __init__.py           # 包初始化
│   ├── config.json           # 项目配置
│   └── project.py            # 项目实现
└── t2d_1/                    # 示例项目：表格到文档转换
    ├── __init__.py           # 包初始化
    ├── config.json           # 项目配置
    └── project.py            # 项目实现
```

## 安装

### 前提条件

- Python 3.8+
- 依赖库：PIL, pandas, requests, tqdm

### 安装步骤

1. 克隆仓库：
```bash
git clone <repository-url>
cd autoprompt
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

1. 配置项目：修改对应项目目录下的 `config.json` 文件
2. 运行主程序：
```bash
python main.py
```

### 选择项目

在 `main.py` 中修改 `project` 变量来选择要运行的项目：

```python
# 选择项目: keytext, t2d_1, headline
project = "keytext"  # 可以替换为其他项目名称
```

## 配置说明

配置文件 (`config.json`) 包含以下主要部分：

### 任务配置 (task_config)

```json
"task_config": {
    "task_id": "apdemo",
    "task": "will be defined in the project file"
}
```

- `task_id`: 任务唯一标识符
- `task`: 任务描述（在项目文件中定义）

### 训练配置 (train_config)

```json
"train_config": {
    "epoch": 1,
    "batch_size": 10,
    "history_size": 20,
    "train_sample_file": "/path/to/train/samples.csv",
    "eval_sample_file": "/path/to/eval/samples.csv",
}
```

- `epoch`: 训练轮数
- `batch_size`: 批处理大小
- `history_size`: 历史记录保存的最大条目数
- `train_sample_file`: 训练样本文件路径（CSV格式）
- `eval_sample_file`: 评估样本文件路径（CSV格式）

### 模型配置 (model_config)

```json
"model_config": {
    "model_name": "gpt-4o-2024-11-20",
    "api_base": "https://api-endpoint-url",
    "max_tokens": 16384,
    "timeout": 100
}
```

- `model_name`: 使用的模型名称
- `api_base`: API 端点 URL
- `max_tokens`: 最大生成令牌数
- `timeout`: 请求超时时间（秒）

## 项目模块

### AutoPromptEngine

`autoprompt/autopromptengine.py` 中的 `AutoPromptEngine` 类是框架的核心，提供了以下功能：

- 初始化配置和日志
- 加载训练样本
- 执行学生-教师迭代学习
- 处理模型推理和结果解析
- 记录学习过程和结果

### 项目实现

每个项目需要继承 `AutoPromptEngine` 类并实现以下方法：

- `set_task_prompt()`: 设置任务提示
- `define_modal_label()`: 定义模态标签和教师标签
- `set_teacher_prompt_template()`: 设置教师提示模板
- `load_samples()`: 加载样本数据

## 示例项目

### KeyText

KeyText 项目演示了如何从图像中提取关键文本信息：

1. 学生模型尝试从图像中提取关键文本
2. 教师模型评估学生的表现并提供改进建议
3. 通过迭代优化提示，提高学生模型的文本提取能力

### Headline

Headline 项目专注于评估广告标题的质量：

1. 学生模型根据给定规则评估广告标题的质量
2. 教师模型评估学生的评分并提供改进建议
3. 通过迭代优化提示，提高学生模型的标题评估能力

### T2D_1

T2D_1 项目（Table to Document）专注于将表格数据转换为结构化文档：

1. 学生模型尝试将表格数据转换为文档格式
2. 教师模型评估转换质量并提供改进建议
3. 通过迭代优化提示，提高学生模型的表格转换能力

### 配置示例

```json
{
    "task_config": {
        "task_id": "apdemo",
        "task": "will be defined in the project file"
    },
    "train_config": {
        "epoch": 1,
        "batch_size": 10,
        "history_size": 20,
        "train_sample_file": "/path/to/user_name.csv",
        "eval_sample_file": "/path/to/test_sample.csv",
    },
    "model_config": {
        "model_name": "gpt-4o-2024-11-20",
        "api_base": "https://api-endpoint-url",
        "max_tokens": 16384,
        "timeout": 100
    }
}
```

## 创建新项目

要创建新项目，请按照以下步骤操作：

1. 在 `autoprompt` 目录下创建新的项目目录
2. 创建 `__init__.py` 文件
3. 创建 `project.py` 文件，实现继承自 `AutoPromptEngine` 的项目类，必须实现以下方法：
   - `set_task_prompt()`
   - `define_modal_label()`
   - `set_teacher_prompt_template()`
   - `load_samples()`
4. 创建 `config.json` 配置文件，包含 `task_config`、`train_config` 和 `model_config` 部分
5. 在 `main.py` 中修改 `project` 变量以使用新项目

## 故障排除

### 常见问题

1. **API 请求失败**
   - 检查 API 密钥和端点 URL 是否正确
   - 确认网络连接正常
   - 查看日志文件获取详细错误信息

2. **图像处理错误**
   - 确保图像文件路径正确
   - 检查图像格式是否受支持
   - 确认 PIL 库正确安装

3. **JSON 解析错误**
   - 检查模型输出是否符合预期格式
   - 调整提示以引导模型生成正确格式的输出

## 日志

框架会生成日志文件 `autopromptengine.log`，记录运行过程中的关键信息、警告和错误。查看此日志可以帮助诊断问题和了解学习过程。


# autogen 安装
conda create -n autogen python=3.12
conda activate autogen

pip install -U "autogen-agentchat"
pip install "autogen-ext[openai]"
pip install "autogen-ext[azure]"