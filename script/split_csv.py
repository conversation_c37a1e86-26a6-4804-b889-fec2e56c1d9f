import os
import pandas as pd
import argparse
from pathlib import Path

def split_csv(input_file, train_ratio=0.8, output_dir=None):
    """
    Split a CSV file into training and evaluation sets.
    
    Args:
        input_file (str): Path to the input CSV file
        train_ratio (float): Ratio of data to use for training (0-1)
        output_dir (str): Directory to save output files (defaults to input file directory)
    
    Returns:
        tuple: Paths to the created train and eval files
    """
    # Validate input file
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"Input file not found: {input_file}")
    
    # Determine output directory and filenames
    input_path = Path(input_file)
    if output_dir is None:
        output_dir = input_path.parent
    else:
        os.makedirs(output_dir, exist_ok=True)
    
    base_name = input_path.stem
    train_file = os.path.join(output_dir, f"{base_name}_train.csv")
    eval_file = os.path.join(output_dir, f"{base_name}_eval.csv")
    
    # Read the CSV file
    try:
        # Try to detect the delimiter
        with open(input_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            if '\t' in first_line:
                delimiter = '\t'
            else:
                delimiter = ','
        
        df = pd.read_csv(input_file, delimiter=delimiter)
    except Exception as e:
        raise ValueError(f"Error reading CSV file: {str(e)}")
    
    # Shuffle the dataframe
    df_shuffled = df.sample(frac=1, random_state=42)
    
    # Split into train and eval sets
    train_size = int(len(df_shuffled) * train_ratio)
    train_df = df_shuffled.iloc[:train_size]
    eval_df = df_shuffled.iloc[train_size:]
    
    # Save to files using the same delimiter as the input file
    train_df.to_csv(train_file, index=False, sep=delimiter)
    eval_df.to_csv(eval_file, index=False, sep=delimiter)
    
    print(f"Split complete:")
    print(f"  - Training set: {train_file} ({len(train_df)} samples)")
    print(f"  - Evaluation set: {eval_file} ({len(eval_df)} samples)")
    
    return train_file, eval_file

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Split a CSV file into training and evaluation sets")
    parser.add_argument("input_file", help="Path to the input CSV file")
    parser.add_argument("--train-ratio", type=float, default=0.8, help="Ratio of data to use for training (default: 0.8)")
    parser.add_argument("--output-dir", help="Directory to save output files (default: same as input file)")
    
    args = parser.parse_args()
    split_csv(args.input_file, args.train_ratio, args.output_dir)