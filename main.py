
import os
import json
import sys
import time
import importlib
sys.path.append(os.path.dirname(os.path.dirname((os.path.dirname(os.path.abspath(__file__))))))

# select project: keytext, t2d_1, headline
# select train_or_eval: train, eval
project = "headline"
train_or_eval = "eval"


# load project 
module = importlib.import_module(project + ".project")
ap_train = getattr(module, 'APTrain')


# load config
cur_dir = os.getcwd()
config_file = os.path.join(cur_dir, project, "config.json")
with open(config_file, "r", encoding="utf-8") as f:
    config_dict = json.load(f)


# ap train
ap_train = getattr(module, 'APTrain')(config = config_dict, train_or_eval=train_or_eval)
ap_train.run()


